# 股票数据采集与展示平台

本项目为一个基于 Flask + APScheduler + SQLAlchemy 的后端服务与 Next.js + React 的前端应用，支持定时采集股票指数数据、用户认证、数据可视化展示，适用于金融数据分析与展示场景。

---

## 1. 项目简介与主要功能

- **股票数据采集**：后端定时任务自动抓取主流股票指数数据，存储于数据库。
- **API服务**：提供 RESTful API，供前端及第三方系统访问股票数据。
- **前端展示**：基于 Next.js/React 实现数据可视化、用户认证（NextAuth）、交互体验。
- **用户认证**：支持 OAuth 登录，安全访问数据。
- **定时任务管理**：APScheduler 定时调度采集任务，支持灵活配置。
- **环境配置**：支持多环境配置与敏感信息隔离。

---

## 2. 目录结构说明

```
FI/
├── backend/                # 后端服务（Flask, APScheduler, SQLAlchemy）
│   ├── app.py              # 主应用入口
│   ├── config.toml         # 主配置文件
│   ├── requirements.txt    # 后端依赖列表
│   ├── db/                 # 数据库模型与连接
│   ├── services/           # 业务逻辑（如股票数据采集）
│   ├── tasks/              # 定时任务调度
│   ├── docs/               # 后端文档
│   └── tests/              # 后端测试
├── frontend/               # 前端应用（Next.js, React）
│   ├── package.json        # 前端依赖列表
│   ├── .env.example        # 环境变量示例
│   ├── src/                # 前端源码
│   │   ├── app/            # 页面与路由
│   │   ├── components/     # 组件
│   │   ├── services/       # API 封装
│   │   ├── styles/         # 样式
│   │   └── types/          # 类型定义
│   └── public/             # 静态资源
├── docker-compose.yml      # 一键部署配置
└── README.md               # 项目说明文档
```

---

## 3. 安装与启动步骤

### 后端（Flask）

1. 安装 Python 3.9+ 环境
2. 安装依赖
   ```bash
   cd backend
   pip install -r requirements.txt
   ```
3. 配置环境变量与数据库（参考 `config.toml`）
4. 启动服务
   ```bash
   python app.py

   # 或者
   flask run --host=0.0.0.0 --port=6008 --reload
   APP_ENV=dev flask run --host=0.0.0.0 --port=6008 --reload
   ```
5. （可选）使用 Docker 部署
   ```bash
   docker-compose up backend
   ```

### 前端（Next.js）

1. 安装 Node.js 18+ 环境
2. 安装依赖
   ```bash
   cd frontend
   yarn install
   ```
3. 配置环境变量（复制 `.env.example` 为 `.env` 并填写实际值）
4. 启动开发服务
   ```bash
   yarn dev
   ```
5. 构建与生产启动
   ```bash
   yarn build
   yarn start
   ```
6. （可选）使用 Docker 部署
   ```bash
   docker-compose up frontend
   ```

---

## 4. 环境变量与配置说明

- 后端配置文件：`backend/config.toml`
  - 数据库连接、定时任务参数、API密钥等
- 前端环境变量：`frontend/.env`
  - API 地址、OAuth 配置等
- 示例文件：`frontend/.env.example`
- 部署建议：敏感信息请勿提交至版本库

---

## 5. API接口文档入口

- 后端接口文档：[`backend/docs/STOCK_DATA_MODEL.md`](backend/docs/STOCK_DATA_MODEL.md:1)
- 主要接口：
  - `/api/indices`：获取股票指数列表
  - `/api/indices/{symbol}`：获取指定指数详情
  - `/api/auth`：用户认证相关接口
- 更多接口与参数详见后端 docs 目录

---

## 6. 定时任务与数据采集说明

- 定时任务调度：基于 APScheduler，配置见 `backend/tasks/cron_job.py`
- 数据采集服务：`backend/services/yahoo_finance_service.py` 实现股票数据抓取
- 任务配置与启动：可在 `config.toml` 中调整采集频率与目标指数
- 采集流程：
  1. APScheduler 定时触发采集任务
  2. 通过 Yahoo Finance API 获取数据
  3. 数据存入数据库，供前端/第三方访问

---

## 7. 常见问题与运维建议

- **端口冲突**：确保前后端端口（默认 5000/6006）未被占用
- **数据库连接失败**：检查 config.toml 配置与数据库服务状态
- **依赖安装报错**：确认 Python/Node 版本与网络环境
- **定时任务未执行**：检查 APScheduler 配置与日志输出
- **环境变量缺失**：参考 .env.example 补全所有必需项
- **API 无法访问**：确认后端服务已启动且前端 API 地址配置正确
- **Docker 部署失败**：检查 docker-compose.yml 配置与镜像拉取情况

---

如有更多问题或定制需求，请查阅 docs 目录或联系项目维护者。
