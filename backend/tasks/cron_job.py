import logging
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from datetime import datetime
from backend.services.yahoo_finance_service import YahooFinanceService

logger = logging.getLogger("cron_job")
logger.setLevel(logging.INFO)

def fetch_major_indices_job():
    logger.info(f"[定时任务] 开始拉取A股主要宽基指数日线行情: {datetime.now()}")
    try:
        service = YahooFinanceService()
        results = service.fetch_all_major_indices()
        for symbol, (success, msg) in results.items():
            if success:
                logger.info(f"[{symbol}] 拉取成功: {msg}")
            else:
                logger.error(f"[{symbol}] 拉取失败: {msg}")
    except Exception as e:
        logger.exception(f"[定时任务] 拉取过程发生异常: {e}")

def start_scheduler(cron_expr: str = "0 17 * * *"):
    """
    启动APScheduler定时任务
    :param cron_expr: cron表达式，默认每天17:00执行
    """
    scheduler = BackgroundScheduler()
    trigger = CronTrigger.from_crontab(cron_expr)
    scheduler.add_job(fetch_major_indices_job, trigger, id="fetch_major_indices_job", replace_existing=True)
    scheduler.start()
    logger.info(f"[定时任务] APScheduler已启动，任务时间: {cron_expr}")