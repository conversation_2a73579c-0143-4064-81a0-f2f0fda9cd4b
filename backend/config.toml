# API配置
[api]
PORT = 6008
HOST = "0.0.0.0"
DEBUG = true

# CORS配置
[cors]
CORS_ORIGINS = "http://localhost:6006,https://fi.01sworld.top:8443"

# AI 模型配置
[ai]
LLM_API_KEY = "sk-pqkelnO3f7F5eW8_yj5OpGk_b-Wn73AxYRqM00uqMAZd97U2k0YDWO-r2OM"
LLM_MODEL_NAME = "deepseek-v3"
LLM_BASE_URL = "https://oneapi.01sworld.top:8443/v1"

# Postgres 数据库配置
[postgres]
host = "*************"
port = 5432
user = "user"
password = "pass123"
database = "fi"

# MinIO S3 配置
[minio]
endpoint = "https://oss-api.01sworld.top:8443"
access_key = "mUpZulr9zy9oKiEK4Ph7"
secret_key = "05oecQnnTIMNkd7M6tMDPpuU3OLWct7h4PAUruby"
bucket = "fi"
region = ""  # 可选

# 定时任务配置
[cron_job]
CRON_JOB_EXPR = "0 17 * * *"  # 每天17:00自动拉取A股主要宽基指数日线行情