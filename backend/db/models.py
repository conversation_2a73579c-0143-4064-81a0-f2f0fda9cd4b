from datetime import datetime, date
from enum import Enum
from decimal import Decimal
from typing import Optional

from sqlalchemy import (
    Column, Integer, String, Text, DateTime, Date, 
    Boolean, Numeric, BigInteger, CheckConstraint, ForeignKey,
    UniqueConstraint, Index
)
from sqlalchemy.orm import declarative_base, relationship, validates
from sqlalchemy.dialects.postgresql import ENUM

Base = declarative_base()

class IndexCategory(str, Enum):
    """指数分类枚举"""
    BROAD_MARKET = "broad_market"    # 宽基指数
    INDUSTRY = "industry"           # 行业指数  
    THEME = "theme"                 # 主题指数
    COMPOSITE = "composite"         # 综合指数

class FetchStatus(str, Enum):
    """数据获取状态枚举"""
    SUCCESS = "success"
    ERROR = "error"
    PENDING = "pending"

class IndexInfo(Base):
    """指数基本信息表"""
    __tablename__ = "index_info"
    
    # 主键字段
    symbol = Column(String(20), primary_key=True, comment="指数代码")
    
    # 基本信息字段
    name = Column(String(100), nullable=False, comment="指数名称")
    exchange = Column(String(10), nullable=False, comment="交易所")
    category = Column(
        ENUM(IndexCategory, name="index_category_enum", create_type=True),
        nullable=False,
        comment="指数分类"
    )
    description = Column(Text, comment="指数描述")
    is_active = Column(Boolean, default=True, nullable=False, comment="是否活跃")
    
    # 时间戳字段
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, comment="创建时间")
    updated_at = Column(
        DateTime, 
        default=datetime.utcnow, 
        onupdate=datetime.utcnow, 
        nullable=False, 
        comment="更新时间"
    )
    
    # 关系定义
    daily_quotes = relationship("DailyQuote", back_populates="index_info", cascade="all, delete-orphan")
    fetch_logs = relationship("DataFetchLog", back_populates="index_info", cascade="all, delete-orphan")
    
    # 索引
    __table_args__ = (
        Index("idx_index_info_category", "category"),
        Index("idx_index_info_exchange", "exchange"),
        Index("idx_index_info_active", "is_active"),
        Index("idx_index_info_name", "name"),
    )
    
    @validates('symbol')
    def validate_symbol(self, key, symbol):
        """验证指数代码格式"""
        if not symbol or len(symbol) > 20:
            raise ValueError("指数代码不能为空且长度不能超过20个字符")
        return symbol
    
    # 移除交易所验证，允许任意交易所值
    
    def __repr__(self):
        return f"<IndexInfo(symbol='{self.symbol}', name='{self.name}')>"

class DailyQuote(Base):
    """日线行情数据表"""
    __tablename__ = "daily_quote"
    
    # 主键字段
    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    
    # 外键字段
    symbol = Column(
        String(20), 
        ForeignKey("index_info.symbol", ondelete="CASCADE"), 
        nullable=False, 
        comment="指数代码"
    )
    
    # 行情数据字段
    trade_date = Column(Date, nullable=False, comment="交易日期")
    open = Column(Numeric(20, 4), nullable=False, comment="开盘价")
    high = Column(Numeric(20, 4), nullable=False, comment="最高价")
    low = Column(Numeric(20, 4), nullable=False, comment="最低价")
    close = Column(Numeric(20, 4), nullable=False, comment="收盘价")
    volume = Column(BigInteger, default=0, nullable=False, comment="成交量")
    change = Column(Numeric(20, 4), comment="涨跌幅")
    change_percent = Column(Numeric(10, 6), comment="涨跌百分比")
    
    # 时间戳字段
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, comment="创建时间")
    
    # 关系定义
    index_info = relationship("IndexInfo", back_populates="daily_quotes")
    
    # 约束和索引
    __table_args__ = (
        UniqueConstraint('symbol', 'trade_date', name='uq_daily_quote_symbol_date'),
        CheckConstraint('high >= low', name='ck_daily_quote_high_low'),
        CheckConstraint('open >= 0', name='ck_daily_quote_open_positive'),
        CheckConstraint('close >= 0', name='ck_daily_quote_close_positive'),
        CheckConstraint('volume >= 0', name='ck_daily_quote_volume_positive'),
        Index("idx_daily_quote_symbol", "symbol"),
        Index("idx_daily_quote_date", "trade_date"),
        Index("idx_daily_quote_symbol_date", "symbol", "trade_date"),
    )
    
    @validates('trade_date')
    def validate_trade_date(self, key, trade_date):
        """验证交易日期"""
        if trade_date > date.today():
            raise ValueError("交易日期不能超过今天")
        return trade_date
    
    @validates('open', 'high', 'low', 'close')
    def validate_price(self, key, value):
        """验证价格数据"""
        if value is not None and value < 0:
            raise ValueError("价格不能为负数")
        return value
    
    def calculate_change_fields(self, prev_close: Optional[Decimal] = None):
        """计算涨跌幅字段

        Args:
            prev_close: 前一交易日收盘价，如果提供则基于此计算涨跌幅，否则基于开盘价
        """
        # 仅在实例有实际值时调用（避免Column类型参与运算）
        close_val = getattr(self, "close", None)

        if close_val is not None:
            if prev_close is not None and prev_close != 0:
                # 基于前一交易日收盘价计算涨跌幅（标准做法）
                self.change = close_val - prev_close
                self.change_percent = (self.change / prev_close) * 100
            else:
                # 如果没有前一交易日数据，则基于开盘价计算
                open_val = getattr(self, "open", None)
                if open_val is not None and open_val != 0:
                    self.change = close_val - open_val
                    self.change_percent = (self.change / open_val) * 100
    
    def __repr__(self):
        return f"<DailyQuote(symbol='{self.symbol}', date='{self.trade_date}')>"

class StockInfo(Base):
    """个股基本信息表"""
    __tablename__ = "stock_info"

    symbol = Column(String(20), primary_key=True, comment="股票代码")
    name = Column(String(100), nullable=False, comment="股票名称")
    industry = Column(String(50), comment="所属行业")
    sector = Column(String(50), comment="所属板块")
    description = Column(Text, comment="股票描述")
    is_active = Column(Boolean, default=True, nullable=False, comment="是否活跃")
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False, comment="更新时间")

    daily_quotes = relationship("StockDailyQuote", back_populates="stock_info", cascade="all, delete-orphan")
    index_components = relationship("IndexComponent", back_populates="stock_info", cascade="all, delete-orphan")

    __table_args__ = (
        Index("idx_stock_info_industry", "industry"),
        Index("idx_stock_info_sector", "sector"),
        Index("idx_stock_info_active", "is_active"),
        Index("idx_stock_info_name", "name"),
    )

    def __repr__(self):
        return f"<StockInfo(symbol='{self.symbol}', name='{self.name}')>"

class StockDailyQuote(Base):
    """个股日线行情数据表"""
    __tablename__ = "stock_daily_quote"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    symbol = Column(String(20), ForeignKey("stock_info.symbol", ondelete="CASCADE"), nullable=False, comment="股票代码")
    trade_date = Column(Date, nullable=False, comment="交易日期")
    open = Column(Numeric(20, 4), nullable=False, comment="开盘价")
    high = Column(Numeric(20, 4), nullable=False, comment="最高价")
    low = Column(Numeric(20, 4), nullable=False, comment="最低价")
    close = Column(Numeric(20, 4), nullable=False, comment="收盘价")
    volume = Column(BigInteger, default=0, nullable=False, comment="成交量")
    change = Column(Numeric(20, 4), comment="涨跌幅")
    change_percent = Column(Numeric(10, 6), comment="涨跌百分比")
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, comment="创建时间")

    stock_info = relationship("StockInfo", back_populates="daily_quotes")

    __table_args__ = (
        UniqueConstraint('symbol', 'trade_date', name='uq_stock_daily_quote_symbol_date'),
        CheckConstraint('high >= low', name='ck_stock_daily_quote_high_low'),
        CheckConstraint('open >= 0', name='ck_stock_daily_quote_open_positive'),
        CheckConstraint('close >= 0', name='ck_stock_daily_quote_close_positive'),
        CheckConstraint('volume >= 0', name='ck_stock_daily_quote_volume_positive'),
        Index("idx_stock_daily_quote_symbol", "symbol"),
        Index("idx_stock_daily_quote_date", "trade_date"),
        Index("idx_stock_daily_quote_symbol_date", "symbol", "trade_date"),
    )

    def calculate_change_fields(self, prev_close: Optional[Decimal] = None):
        """计算涨跌幅字段

        Args:
            prev_close: 前一交易日收盘价，如果提供则基于此计算涨跌幅，否则基于开盘价
        """
        close_val = getattr(self, "close", None)

        if close_val is not None:
            if prev_close is not None and prev_close != 0:
                # 基于前一交易日收盘价计算涨跌幅（标准做法）
                self.change = close_val - prev_close
                self.change_percent = (self.change / prev_close) * 100
            else:
                # 如果没有前一交易日数据，则基于开盘价计算
                open_val = getattr(self, "open", None)
                if open_val is not None and open_val != 0:
                    self.change = close_val - open_val
                    self.change_percent = (self.change / open_val) * 100

    def __repr__(self):
        return f"<StockDailyQuote(symbol='{self.symbol}', date='{self.trade_date}')>"

class IndexComponent(Base):
    """指数-成分股关联表"""
    __tablename__ = "index_component"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    index_symbol = Column(String(20), ForeignKey("index_info.symbol", ondelete="CASCADE"), nullable=False, comment="指数代码")
    stock_symbol = Column(String(20), ForeignKey("stock_info.symbol", ondelete="CASCADE"), nullable=False, comment="股票代码")
    weight = Column(Numeric(10, 4), comment="权重")  # 可选，部分指数有成分股权重
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, comment="创建时间")

    index_info = relationship("IndexInfo", back_populates="index_components")
    stock_info = relationship("StockInfo", back_populates="index_components")

    __table_args__ = (
        UniqueConstraint('index_symbol', 'stock_symbol', name='uq_index_component_index_stock'),
        Index("idx_index_component_index", "index_symbol"),
        Index("idx_index_component_stock", "stock_symbol"),
    )

    def __repr__(self):
        return f"<IndexComponent(index='{self.index_symbol}', stock='{self.stock_symbol}')>"

# 在IndexInfo中补充index_components关系
IndexInfo.index_components = relationship("IndexComponent", back_populates="index_info", cascade="all, delete-orphan")
class DataFetchLog(Base):
    """数据获取记录表"""
    __tablename__ = "data_fetch_log"
    
    # 主键字段
    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    
    # 外键字段
    symbol = Column(
        String(20), 
        ForeignKey("index_info.symbol", ondelete="CASCADE"), 
        nullable=False, 
        comment="指数代码"
    )
    
    # 获取信息字段
    fetch_date = Column(Date, nullable=False, comment="获取日期")
    status = Column(
        ENUM(FetchStatus, name="fetch_status_enum", create_type=True),
        nullable=False,
        default=FetchStatus.PENDING,
        comment="获取状态"
    )
    error_message = Column(Text, comment="错误信息")
    
    # 时间戳字段
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, comment="创建时间")
    
    # 关系定义
    index_info = relationship("IndexInfo", back_populates="fetch_logs")
    
    # 约束和索引
    __table_args__ = (
        UniqueConstraint('symbol', 'fetch_date', name='uq_fetch_log_symbol_date'),
        Index("idx_fetch_log_symbol", "symbol"),
        Index("idx_fetch_log_date", "fetch_date"),
        Index("idx_fetch_log_status", "status"),
        Index("idx_fetch_log_symbol_date", "symbol", "fetch_date"),
    )
    
    @validates('status')
    def validate_status(self, key, status):
        """验证状态值"""
        try:
            return FetchStatus(status)
        except ValueError:
            raise ValueError(f"无效的状态值: {status}")
    
    def __repr__(self):
        return f"<DataFetchLog(symbol='{self.symbol}', status='{self.status}')>"

# 预定义的A股主要宽基指数
A_SHARE_MAJOR_INDICES = [
    {
        "symbol": "000001.SH",
        "name": "上证指数",
        "exchange": "SH",
        "category": IndexCategory.COMPOSITE,
        "description": "上海证券交易所综合股价指数"
    },
    {
        "symbol": "399001.SZ",
        "name": "深证成指", 
        "exchange": "SZ",
        "category": IndexCategory.COMPOSITE,
        "description": "深圳证券交易所成份股价指数"
    },
    {
        "symbol": "399006.SZ",
        "name": "创业板指",
        "exchange": "SZ", 
        "category": IndexCategory.BROAD_MARKET,
        "description": "创业板指数"
    },
    {
        "symbol": "000688.SH",
        "name": "科创50",
        "exchange": "SH",
        "category": IndexCategory.BROAD_MARKET,
        "description": "科创板50成份指数"
    },
    {
        "symbol": "000016.SH",
        "name": "上证50",
        "exchange": "SH",
        "category": IndexCategory.BROAD_MARKET,
        "description": "上证50指数"
    },
    {
        "symbol": "000300.SH",
        "name": "沪深300",
        "exchange": "SH",
        "category": IndexCategory.BROAD_MARKET,
        "description": "沪深300指数"
    },
    {
        "symbol": "000903.SH",
        "name": "中证100",
        "exchange": "SH",
        "category": IndexCategory.BROAD_MARKET,
        "description": "中证100指数"
    },
    {
        "symbol": "000905.SH",
        "name": "中证500",
        "exchange": "SH",
        "category": IndexCategory.BROAD_MARKET,
        "description": "中证500指数"
    },
    {
        "symbol": "000852.SH",
        "name": "中证1000",
        "exchange": "SH",
        "category": IndexCategory.BROAD_MARKET,
        "description": "中证1000指数"
    },
    {
        "symbol": "932000.CSI",
        "name": "中证2000",
        "exchange": "CSI",
        "category": IndexCategory.BROAD_MARKET,
        "description": "中证2000指数"
    },
    {
        "symbol": "000985.SH",
        "name": "中证全指",
        "exchange": "SH",
        "category": IndexCategory.BROAD_MARKET,
        "description": "中证全指指数"
    }
]