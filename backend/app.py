import sys
import os

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 类型安全转换工具
def safe_float(val):
    try:
        if val is None:
            return None
        # 兼容 Decimal、float、int
        if hasattr(val, "__float__"):
            return float(val)
        return None
    except Exception:
        return None

def safe_int(val):
    try:
        if val is None:
            return None
        if hasattr(val, "__int__"):
            return int(val)
        return None
    except Exception:
        return None

from flask import Flask, request, jsonify
from flask_cors import CORS
from backend.config import config
from backend.tasks.cron_job import start_scheduler
from backend.services.yahoo_finance_service import YahooFinanceService

# 导入数据库session工厂
from backend.db.db import get_engine_and_sessionmaker
from backend.db.models import IndexInfo, DailyQuote

from functools import wraps
import re

# 简单认证：预设 token，实际可接入更完善的认证系统
API_AUTH_TOKEN = config.get('API_AUTH_TOKEN', 'test-token')

def require_auth(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        auth_header = request.headers.get('Authorization', '')
        match = re.match(r'Bearer\s+(.+)', auth_header)
        token = match.group(1) if match else None
        if not token or token != API_AUTH_TOKEN:
            return jsonify({"error": "未认证或Token无效", "status": "unauthorized"}), 401
        return f(*args, **kwargs)
    return decorated

# 初始化Flask应用
app = Flask(__name__)
CORS(app, resources={r"/api/*": {"origins": config.get('CORS_ORIGINS', '*').split(',')}})

# 错误处理中间件
@app.errorhandler(Exception)
def handle_exception(e):
    # 记录错误
    app.logger.error(f"Unhandled exception: {str(e)}")
    
    # 返回友好的错误消息
    return jsonify({
        "error": str(e),
        "status": "error"
    }), 500

# 健康检查接口
@app.route('/api/health', methods=['GET'])
def health_check():
    return jsonify({"status": "ok"})

# 查询所有指数基本信息
@app.route('/api/indices', methods=['GET'])
@require_auth
def get_indices():
    _, SessionLocal = get_engine_and_sessionmaker()
    session = SessionLocal()
    try:
        indices = session.query(IndexInfo).filter(IndexInfo.is_active == True).all()
        result = []
        for idx in indices:
            result.append({
                "symbol": idx.symbol,
                "name": idx.name,
                "exchange": idx.exchange,
                "category": idx.category,
                "description": idx.description,
                "is_active": idx.is_active,
                "created_at": idx.created_at.isoformat() if getattr(idx, "created_at", None) else None,
                "updated_at": idx.updated_at.isoformat() if getattr(idx, "updated_at", None) else None
            })
        return jsonify({"data": result, "status": "ok"})
    except Exception as e:
        app.logger.error(f"get_indices error: {str(e)}")
        return jsonify({"error": "查询失败", "status": "error"}), 500
    finally:
        session.close()

# 查询日线行情（支持时间区间、分页）
@app.route('/api/indices/<symbol>/quotes', methods=['GET'])
@require_auth
def get_daily_quotes(symbol):
    _, SessionLocal = get_engine_and_sessionmaker()
    session = SessionLocal()
    try:
        # 参数校验
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 20))
        if page < 1 or page_size < 1 or page_size > 100:
            return jsonify({"error": "分页参数不合法", "status": "error"}), 400

        query = session.query(DailyQuote).filter(DailyQuote.symbol == symbol)
        if start_date:
            try:
                query = query.filter(DailyQuote.trade_date >= start_date)
            except Exception:
                return jsonify({"error": "start_date格式错误", "status": "error"}), 400
        if end_date:
            try:
                query = query.filter(DailyQuote.trade_date <= end_date)
            except Exception:
                return jsonify({"error": "end_date格式错误", "status": "error"}), 400

        total = query.count()
        quotes = query.order_by(DailyQuote.trade_date.desc()).offset((page - 1) * page_size).limit(page_size).all()
        result = []
        for q in quotes:
            result.append({
                "id": q.id,
                "symbol": q.symbol,
                "trade_date": q.trade_date.isoformat() if getattr(q, "trade_date", None) else None,
                "open": safe_float(getattr(q, "open", None)),
                "high": safe_float(getattr(q, "high", None)),
                "low": safe_float(getattr(q, "low", None)),
                "close": safe_float(getattr(q, "close", None)),
                "volume": safe_int(getattr(q, "volume", None)),
                "change": safe_float(getattr(q, "change", None)),
                "change_percent": safe_float(getattr(q, "change_percent", None)),
                "created_at": q.created_at.isoformat() if getattr(q, "created_at", None) else None
            })
        return jsonify({
            "data": result,
            "total": total,
            "page": page,
            "page_size": page_size,
            "status": "ok"
        })
    except Exception as e:
        app.logger.error(f"get_daily_quotes error: {str(e)}")
        return jsonify({"error": "查询失败", "status": "error"}), 500
    finally:
        session.close()

# 查询最新行情
@app.route('/api/indices/<symbol>/latest', methods=['GET'])
@require_auth
def get_latest_quote(symbol):
    _, SessionLocal = get_engine_and_sessionmaker()
    session = SessionLocal()
    try:
        quote = session.query(DailyQuote).filter(DailyQuote.symbol == symbol).order_by(DailyQuote.trade_date.desc()).first()
        if not quote:
            return jsonify({"error": "未找到最新行情", "status": "error"}), 404
        result = {
            "id": quote.id,
            "symbol": quote.symbol,
            "trade_date": quote.trade_date.isoformat() if getattr(quote, "trade_date", None) else None,
            "open": safe_float(getattr(quote, "open", None)),
            "high": safe_float(getattr(quote, "high", None)),
            "low": safe_float(getattr(quote, "low", None)),
            "close": safe_float(getattr(quote, "close", None)),
            "volume": safe_int(getattr(quote, "volume", None)),
            "change": safe_float(getattr(quote, "change", None)),
            "change_percent": safe_float(getattr(quote, "change_percent", None)),
            "created_at": quote.created_at.isoformat() if getattr(quote, "created_at", None) else None
        }
        return jsonify({"data": result, "status": "ok"})
    except Exception as e:
        app.logger.error(f"get_latest_quote error: {str(e)}")
        return jsonify({"error": "查询失败", "status": "error"}), 500
    finally:
        session.close()

@app.route('/api/fetch-data', methods=['POST'])
@require_auth
def fetch_data():
    try:
        service = YahooFinanceService()
        results = service.fetch_all_major_indices()
        # 格式化结果
        formatted = []
        for symbol, (success, message) in results.items():
            formatted.append({
                "symbol": symbol,
                "success": success,
                "message": message
            })
        return jsonify({
            "status": "ok",
            "task_id": None,
            "message": "已抓取所有主要指数",
            "result": formatted
        }), 200
    except Exception as e:
        app.logger.error(f"fetch_data error: {str(e)}")
        return jsonify({
            "status": "error",
            "task_id": None,
            "message": f"服务异常: {str(e)}",
            "result": None
        }), 500

# 启动应用
if __name__ == '__main__':
    from backend.db.db import init_db
    init_db()
    # 启动定时任务（可通过 config 配置 cron 表达式，默认每天17:00）
    cron_expr = config.get("cron_job", {}).get("CRON_JOB_EXPR", "0 17 * * *")
    start_scheduler(cron_expr)
    
    port = int(config.get('PORT', 6008))
    host = config.get('HOST', '0.0.0.0')
    debug = config.get('DEBUG', False)
    if isinstance(debug, str):
        debug = debug.lower() == 'true'
    
    app.run(host=host, port=port, debug=debug)