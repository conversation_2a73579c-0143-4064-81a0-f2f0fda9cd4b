# 股票数据模型设计文档

## 概述

本文档详细描述了用于存储A股主要宽基指数日线行情数据的数据库模型设计。该设计支持从Yahoo Finance获取的数据存储，包括指数基本信息、日线行情数据和数据获取记录。

## 数据库架构

### 技术栈
- **数据库**: PostgreSQL 12+
- **ORM**: SQLAlchemy 1.4+
- **编程语言**: Python 3.8+

### 表结构总览

系统包含3个主要表：
1. `index_info` - 指数基本信息表
2. `daily_quote` - 日线行情数据表  
3. `data_fetch_log` - 数据获取记录表

## 详细表结构

### 1. 指数基本信息表 (index_info)

| 字段名 | 类型 | 约束 | 描述 |
|--------|------|------|------|
| symbol | VARCHAR(20) | PRIMARY KEY | 指数代码 (主键) |
| name | VARCHAR(100) | NOT NULL | 指数名称 |
| exchange | VARCHAR(10) | NOT NULL | 交易所 (SH/SZ/CSI) |
| category | ENUM | NOT NULL | 指数分类 |
| description | TEXT | NULLABLE | 指数描述 |
| is_active | BOOLEAN | NOT NULL DEFAULT TRUE | 是否活跃 |
| created_at | TIMESTAMP | NOT NULL DEFAULT NOW() | 创建时间 |
| updated_at | TIMESTAMP | NOT NULL DEFAULT NOW() | 更新时间 |

**索引:**
- `idx_index_info_category` (category)
- `idx_index_info_exchange` (exchange) 
- `idx_index_info_active` (is_active)
- `idx_index_info_name` (name)

### 2. 日线行情数据表 (daily_quote)

| 字段名 | 类型 | 约束 | 描述 |
|--------|------|------|------|
| id | SERIAL | PRIMARY KEY | 自增主键 |
| symbol | VARCHAR(20) | FOREIGN KEY, NOT NULL | 指数代码 |
| trade_date | DATE | NOT NULL | 交易日期 |
| open | NUMERIC(20,4) | NOT NULL | 开盘价 |
| high | NUMERIC(20,4) | NOT NULL | 最高价 |
| low | NUMERIC(20,4) | NOT NULL | 最低价 |
| close | NUMERIC(20,4) | NOT NULL | 收盘价 |
| volume | BIGINT | NOT NULL DEFAULT 0 | 成交量 |
| change | NUMERIC(20,4) | NULLABLE | 涨跌幅 |
| change_percent | NUMERIC(10,6) | NULLABLE | 涨跌百分比 |
| created_at | TIMESTAMP | NOT NULL DEFAULT NOW() | 创建时间 |

**约束:**
- `uq_daily_quote_symbol_date` UNIQUE (symbol, trade_date)
- `ck_daily_quote_high_low` CHECK (high >= low)
- `ck_daily_quote_open_positive` CHECK (open >= 0)
- `ck_daily_quote_close_positive` CHECK (close >= 0)
- `ck_daily_quote_volume_positive` CHECK (volume >= 0)

**索引:**
- `idx_daily_quote_symbol` (symbol)
- `idx_daily_quote_date` (trade_date)
- `idx_daily_quote_symbol_date` (symbol, trade_date)

### 3. 数据获取记录表 (data_fetch_log)

| 字段名 | 类型 | 约束 | 描述 |
|--------|------|------|------|
| id | SERIAL | PRIMARY KEY | 自增主键 |
| symbol | VARCHAR(20) | FOREIGN KEY, NOT NULL | 指数代码 |
| fetch_date | DATE | NOT NULL | 获取日期 |
| status | ENUM | NOT NULL DEFAULT 'pending' | 获取状态 |
| error_message | TEXT | NULLABLE | 错误信息 |
| created_at | TIMESTAMP | NOT NULL DEFAULT NOW() | 创建时间 |

**约束:**
- `uq_fetch_log_symbol_date` UNIQUE (symbol, fetch_date)

**索引:**
- `idx_fetch_log_symbol` (symbol)
- `idx_fetch_log_date` (fetch_date)
- `idx_fetch_log_status` (status)
- `idx_fetch_log_symbol_date` (symbol, fetch_date)

## 枚举类型定义

### 指数分类 (IndexCategory)
- `broad_market` - 宽基指数 (沪深300、上证50等)
- `industry` - 行业指数 (证券、银行、医药等)
- `theme` - 主题指数 (人工智能、新能源等)
- `composite` - 综合指数 (上证指数、深证成指)

### 数据获取状态 (FetchStatus)
- `success` - 获取成功
- `error` - 获取失败
- `pending` - 等待获取

## 数据验证规则

### 指数基本信息验证
1. 指数代码不能为空且长度≤20字符
2. 交易所必须是SH、SZ或CSI
3. 分类必须是预定义的枚举值

### 日线数据验证
1. 交易日期不能超过当前日期
2. 最高价必须≥最低价
3. 所有价格字段必须≥0
4. 成交量必须≥0
5. 同一指数同一日期不能有重复记录

### 数据获取验证
1. 状态必须是预定义的枚举值
2. 同一指数同一获取日期不能有重复记录

## 表关系设计

### 一对多关系
- `index_info`(1) → `daily_quote`(N)
- `index_info`(1) → `data_fetch_log`(N)

### 外键约束
- 所有外键都使用ON DELETE CASCADE
- 确保数据完整性

## 索引优化策略

### 查询优化索引
1. **按分类查询**: `idx_index_info_category`
2. **按交易所查询**: `idx_index_info_exchange`
3. **按日期范围查询**: `idx_daily_quote_date`
4. **按指数和日期查询**: `idx_daily_quote_symbol_date`
5. **状态查询**: `idx_fetch_log_status`

### 唯一性约束
1. 防止重复日线数据: `uq_daily_quote_symbol_date`
2. 防止重复获取记录: `uq_fetch_log_symbol_date`

## 数据库迁移策略

### 初始迁移
```bash
# 创建数据库表
python -m backend.db.db
```

### 增量迁移
使用Alembic进行数据库版本管理：
1. 安装Alembic: `pip install alembic`
2. 初始化: `alembic init alembic`
3. 生成迁移脚本: `alembic revision --autogenerate -m "description"`
4. 执行迁移: `alembic upgrade head`

### 数据初始化
系统预定义了11个A股主要宽基指数：
```python
A_SHARE_MAJOR_INDICES = [
    {"symbol": "000001.SH", "name": "上证指数", ...},
    {"symbol": "399001.SZ", "name": "深证成指", ...},
    # ... 其他指数
]
```

## 性能考虑

### 数据量估算
- 指数基本信息: ~100条记录
- 日线数据: 100指数 × 250交易日/年 × 10年 = 250,000条
- 获取记录: 100指数 × 365天/年 × 10年 = 365,000条

### 查询优化
1. 分区表: 按年份对日线数据表进行分区
2. 归档策略: 将历史数据移动到归档表
3. 定期维护: 定期执行VACUUM和ANALYZE

## 扩展性设计

### 未来扩展
1. **分钟线数据**: 添加`minute_quote`表
2. **财务指标**: 添加`financial_metric`表
3. **成分股信息**: 添加`constituent_stock`表
4. **行业分类**: 细化行业指数分类

### API接口
设计RESTful API接口用于：
- 查询指数列表
- 获取日线数据
- 管理数据获取任务
- 监控数据质量

## 部署建议

### 环境配置
1. **开发环境**: 使用本地PostgreSQL
2. **测试环境**: 使用Docker容器
3. **生产环境**: 使用云数据库服务

### 监控告警
1. 数据获取失败监控
2. 数据质量检查
3. 数据库性能监控

## 总结

该数据库设计提供了：
1. **完整的数据模型**: 覆盖指数信息、行情数据和获取记录
2. **强大的数据验证**: 确保数据质量和一致性
3. **优秀的查询性能**: 通过合理的索引设计
4. **良好的扩展性**: 支持未来功能扩展
5. **易于维护**: 清晰的表结构和关系设计

此设计能够有效支持从Yahoo Finance获取的A股指数数据存储和分析需求。