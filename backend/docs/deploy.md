# 后端定时任务系统部署与运维说明

## 1. APScheduler 定时任务配置

- 定时任务用于自动拉取A股主要宽基指数日线行情，核心逻辑见 [`backend/tasks/cron_job.py`](../tasks/cron_job.py)。
- 任务调度时间与参数通过 [`backend/config.toml`](../config.toml) 配置，示例：

```toml
[cron_job]
CRON_JOB_EXPR = "0 17 * * *"  # 每天17:00自动执行
```
- 可根据实际需求调整 `CRON_JOB_EXPR`，支持标准 cron 表达式。

## 2. Docker 容器化部署

- 构建后端镜像（需在项目根目录）：
  ```bash
  docker-compose build backend
  ```
- 启动服务（含定时任务）：
  ```bash
  docker-compose up -d
  ```
- 配置文件挂载路径已在 `docker-compose.yml` 配置，无需额外操作。

## 3. 日志与任务验证

- APScheduler 日志输出在容器标准输出，可通过以下命令查看：
  ```bash
  docker-compose logs backend
  ```
- 日志关键字：
  - `[定时任务] APScheduler已启动，任务时间: ...`
  - `[定时任务] 开始拉取A股主要宽基指数日线行情: ...`
  - `[symbol] 拉取成功/失败: ...`
  - 异常时有 `[定时任务] 拉取过程发生异常: ...`
- 验证方法：
  1. 启动服务后，观察日志是否有 APScheduler 启动与任务执行记录。
  2. 可临时将 `CRON_JOB_EXPR` 设置为每分钟执行（如 `"* * * * *"`），便于测试。

## 4. 生产环境参数调整建议

- 所有定时任务参数均可通过 `config.toml` 配置，无需重启容器，直接修改挂载的配置文件后重启服务即可。
- 推荐定期检查日志，确保任务稳定运行。
- 如需调整任务逻辑或采集范围，请修改 [`backend/tasks/cron_job.py`](../tasks/cron_job.py) 并重建镜像。

## 5. 常见问题

- **Flask 未安装/依赖缺失**：请确保 requirements.txt 中包含 flask、apscheduler 等依赖，容器会自动安装。
- **定时任务未执行**：请检查 config.toml 的 cron 表达式配置，确认日志输出。
- **配置未生效**：确认 docker-compose.yml 挂载路径正确，且容器内 `/app/config.toml` 文件已更新。

---
如有更多运维需求或定制化，请联系开发团队。