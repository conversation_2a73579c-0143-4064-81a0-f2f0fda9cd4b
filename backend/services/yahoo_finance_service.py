import yfinance as yf
import pandas as pd
from datetime import date, timedelta, datetime
import time
import logging
from typing import List, Dict, Optional, Tuple, Any, Callable
from decimal import Decimal
from sqlalchemy.orm import Session

from backend.db.models import (
    IndexInfo, DailyQuote, DataFetchLog, FetchStatus,
    A_SHARE_MAJOR_INDICES
)
from backend.db.db import get_engine_and_sessionmaker

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class YahooFinanceService:
    """Yahoo Finance数据获取服务类"""

    def _ensure_index_info_exists(self, symbol: str, session: Session):
        """确保 symbol 已存在于 index_info，若不存在则插入默认 index_info"""
        existing_index_info = session.query(IndexInfo).filter(IndexInfo.symbol == symbol).first()
        if not existing_index_info:
            # 合法枚举值
            valid_categories = {"broad_market", "industry", "theme", "composite"}
            # 尝试根据 symbol 推断 exchange，不做验证限制
            exchange = "UNKNOWN"  # 默认值
            if symbol.endswith(".SH"):
                exchange = "SH"
            elif symbol.endswith(".SZ"):
                exchange = "SZ"
            elif symbol.endswith(".CSI"):
                exchange = "CSI"
            # 默认分类
            category = "composite"
            if category not in valid_categories:
                logger.warning(f"Symbol {symbol} category '{category}' 非法，已自动替换为 'composite'")
                category = "composite"
            index_info = IndexInfo(symbol=symbol, name=symbol, category=category, exchange=exchange)
            session.add(index_info)
            session.commit()

    def __init__(self, session: Optional[Session] = None):
        self.session = session
        self.max_retries = 3
        self.retry_delay = 2

    def _get_session(self) -> Session:
        if self.session is not None:
            return self.session
        _, SessionLocal = get_engine_and_sessionmaker()
        return SessionLocal()

    def _convert_to_yahoo_symbol(self, symbol: str) -> str:
        """将本地符号格式转换为Yahoo Finance格式"""
        if symbol.endswith('.SH'):
            return symbol.replace('.SH', '.SS')
        elif symbol.endswith('.SZ'):
            return symbol.replace('.SZ', '.SZ')  # 深交所保持.SZ格式
        elif symbol.endswith('.CSI'):
            # CSI指数通常需要去掉后缀或使用特定格式
            base_code = symbol.replace('.CSI', '')
            return f"{base_code}.CSI"
        return symbol

    def _convert_from_yahoo_symbol(self, yahoo_symbol: str) -> str:
        if yahoo_symbol.endswith('.SS'):
            return yahoo_symbol.replace('.SS', '.SH')
        elif yahoo_symbol.endswith('.SZ'):
            return yahoo_symbol.replace('.SZ', '.SZ')
        return yahoo_symbol

    def _get_last_trade_date(self, symbol: str, session: Session) -> Optional[date]:
        last_quote = session.query(DailyQuote).filter(
            DailyQuote.symbol == symbol
        ).order_by(DailyQuote.trade_date.desc()).first()
        if last_quote and isinstance(last_quote.trade_date, date):
            return last_quote.trade_date
        return None

    def _create_fetch_log(self, symbol: str, fetch_date: date,
                         status: FetchStatus, error_message: Optional[str] = None,
                         session: Optional[Session] = None) -> DataFetchLog:
        if session is None:
            session = self._get_session()
        # 插入 DataFetchLog 前确保 symbol 已在 index_info
        self._ensure_index_info_exists(symbol, session)
        fetch_log = DataFetchLog(
            symbol=symbol,
            fetch_date=fetch_date,
            status=status,
            error_message=error_message
        )
        session.add(fetch_log)
        session.commit()
        return fetch_log

    def _retry_with_backoff(self, func: Callable, *args: Any, **kwargs: Any) -> Any:
        for attempt in range(self.max_retries):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                if attempt == self.max_retries - 1:
                    raise e
                delay = self.retry_delay * (2 ** attempt)
                logger.warning(f"Attempt {attempt + 1} failed: {e}. Retrying in {delay} seconds...")
                time.sleep(delay)

    def fetch_index_data(self, symbol: str, start_date: Optional[date] = None, 
                        end_date: Optional[date] = None, incremental: bool = True) -> Tuple[bool, str]:
        session = self._get_session()
        today = date.today()
        try:
            if end_date is None:
                end_date = today
            if start_date is None:
                if incremental:
                    last_date = self._get_last_trade_date(symbol, session)
                    start_date = last_date + timedelta(days=1) if last_date else date(2025, 1, 1)
                else:
                    start_date = date(2025, 1, 1)
            if start_date > end_date:
                logger.info(f"No new data for {symbol} from {start_date} to {end_date}")
                self._create_fetch_log(symbol, today, FetchStatus.SUCCESS, "No new data available", session)
                return True, "No new data available"
            yahoo_symbol = self._convert_to_yahoo_symbol(symbol)
            def _fetch_data():
                ticker = yf.Ticker(yahoo_symbol)
                data = ticker.history(
                    start=start_date,
                    end=end_date + timedelta(days=1),
                    interval="1d"
                )
                return data
            data = self._retry_with_backoff(_fetch_data)
            if data is None or data.empty:
                logger.info(f"No data found for {symbol} from {start_date} to {end_date}")
                self._create_fetch_log(symbol, today, FetchStatus.SUCCESS, "No data available", session)
                return True, "No data available"
            success_count = self._process_and_save_data(symbol, data, session)
            logger.info(f"Successfully fetched {success_count} records for {symbol}")
            self._create_fetch_log(symbol, today, FetchStatus.SUCCESS, f"Fetched {success_count} records", session)
            return True, f"Fetched {success_count} records"
        except Exception as e:
            error_msg = f"Failed to fetch data for {symbol}: {str(e)}"
            logger.error(error_msg)
            self._create_fetch_log(symbol, today, FetchStatus.ERROR, error_msg, session)
            return False, error_msg
        finally:
            if self.session is None:
                session.close()

    def fetch_multiple_indices(self, symbols: List[str], **kwargs: Any) -> Dict[str, Tuple[bool, str]]:
        results = {}
        for symbol in symbols:
            success, message = self.fetch_index_data(symbol, **kwargs)
            results[symbol] = (success, message)
            time.sleep(0.5)
        return results

    def fetch_all_major_indices(self, **kwargs: Any) -> Dict[str, Tuple[bool, str]]:
        symbols = [index["symbol"] for index in A_SHARE_MAJOR_INDICES]
        return self.fetch_multiple_indices(symbols, **kwargs)

    def _get_previous_close(self, symbol: str, trade_date: date, session: Session) -> Optional[Decimal]:
        """获取前一交易日的收盘价"""
        prev_quote = session.query(DailyQuote).filter(
            DailyQuote.symbol == symbol,
            DailyQuote.trade_date < trade_date
        ).order_by(DailyQuote.trade_date.desc()).first()

        if prev_quote and prev_quote.close is not None:
            return Decimal(str(prev_quote.close))
        return None

    def _process_and_save_data(self, symbol: str, data: pd.DataFrame, session: Session) -> int:
        success_count = 0
        # 将数据按日期排序，确保按时间顺序处理
        data_sorted = data.sort_index()

        for index, row in data_sorted.iterrows():
            try:
                # 兼容pandas的Timestamp、datetime、date、str
                try:
                    if isinstance(index, pd.Timestamp):
                        trade_date = index.date()
                    elif isinstance(index, (datetime, date)):
                        trade_date = index if isinstance(index, date) else index.date()
                    elif isinstance(index, str):
                        trade_date = pd.to_datetime(index).date()
                    else:
                        trade_date = pd.to_datetime(str(index)).date()
                except Exception:
                    logger.error(f"无法解析trade_date: {index}")
                    continue

                if pd.isna(row['Open']) or pd.isna(row['Close']):
                    continue

                quote = DailyQuote(
                    symbol=symbol,
                    trade_date=trade_date,
                    open=Decimal(str(row['Open'])),
                    high=Decimal(str(row['High'])),
                    low=Decimal(str(row['Low'])),
                    close=Decimal(str(row['Close'])),
                    volume=int(row['Volume']) if not pd.isna(row['Volume']) else 0
                )

                # 获取前一交易日收盘价来计算涨跌幅
                prev_close = self._get_previous_close(symbol, trade_date, session)
                quote.calculate_change_fields(prev_close)

                existing_quote = session.query(DailyQuote).filter(
                    DailyQuote.symbol == symbol,
                    DailyQuote.trade_date == trade_date
                ).first()

                if existing_quote:
                    existing_quote.open = quote.open
                    existing_quote.high = quote.high
                    existing_quote.low = quote.low
                    existing_quote.close = quote.close
                    existing_quote.volume = quote.volume
                    # 重新计算涨跌幅
                    prev_close = self._get_previous_close(symbol, trade_date, session)
                    existing_quote.calculate_change_fields(prev_close)
                else:
                    # 插入 daily_quote 前，确保 symbol 已在 index_info，不存在则插入 index_info
                    self._ensure_index_info_exists(symbol, session)
                    session.add(quote)
                success_count += 1
            except Exception as e:
                logger.error(f"Error processing data for {symbol} on {index}: {e}")
                continue
        session.commit()
        return success_count

    def initialize_index_info(self) -> int:
        session = self._get_session()
        success_count = 0
        try:
            # 先查出所有已存在 symbol，整体去重
            existing_symbols = set(
                s[0] for s in session.query(IndexInfo.symbol).all()
            )
            # 仅插入未存在的 symbol，跳过已存在
            valid_categories = {"broad_market", "industry", "theme", "composite"}
            for index_data in A_SHARE_MAJOR_INDICES:
                symbol = index_data["symbol"]
                # 校验 category 字段
                category = index_data.get("category", "composite")
                if category not in valid_categories:
                    logger.warning(f"Symbol {symbol} category '{category}' 非法，已自动替换为 'composite'")
                    index_data["category"] = "composite"
                if symbol in existing_symbols:
                    continue  # 跳过已存在 symbol，避免唯一约束冲突
                try:
                    index_info = IndexInfo(**index_data)
                    session.add(index_info)
                    success_count += 1
                except Exception as e:
                    logger.error(f"Error initializing index {symbol}: {e}")
                    continue
            session.commit()
            return success_count
        finally:
            if self.session is None:
                session.close()