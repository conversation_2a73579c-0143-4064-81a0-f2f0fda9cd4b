// CORS配置 - 根据环境区分设置

const corsConfig = {
  // 开发环境配置
  development: {
    allowedOrigins: [
      'https://fi.01sworld.top',
      'http://localhost:3000',
      'http://localhost:6006',
      'http://127.0.0.1:6006'
    ],
    allowCredentials: true,
    allowedMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
  },
  
  // 生产环境配置
  production: {
    allowedOrigins: [
      // 生产环境需要跨域时，在这里添加允许的域名
      // 'https://your-production-domain.com',
      // 'https://api.your-domain.com'
    ],
    allowCredentials: false,
    allowedMethods: ['GET', 'POST'],
    allowedHeaders: ['Content-Type', 'Authorization']
  },
  
  // 测试环境配置
  test: {
    allowedOrigins: ['http://localhost:3000'],
    allowCredentials: true,
    allowedMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization']
  }
};

/**
 * 获取当前环境的CORS配置
 * @param {string} env - 环境名称 (development, production, test)
 * @returns {object} CORS配置对象
 */
function getCorsConfig(env = process.env.NODE_ENV || 'development') {
  return corsConfig[env] || corsConfig.development;
}

/**
 * 检查域名是否被允许
 * @param {string} origin - 请求的域名
 * @param {string} env - 环境名称
 * @returns {boolean} 是否允许该域名
 */
function isOriginAllowed(origin, env = process.env.NODE_ENV || 'development') {
  const config = getCorsConfig(env);
  return config.allowedOrigins.includes(origin);
}

module.exports = {
  corsConfig,
  getCorsConfig,
  isOriginAllowed
};
