// 指数行情相关类型
export interface IndexItem {
  symbol: string;
  name: string;
  price: number;
  change: number;
  percent: number;
}

export interface IndexDetail {
  symbol: string;
  name: string;
  price: number;
  open: number;
  high: number;
  low: number;
  prevClose: number;
  change: number;
  percent: number;
  volume: number;
  turnover: number;
  time: string;
}

// API 错误响应类型
export interface ApiErrorResponse {
  error: string;
  status: string;
}