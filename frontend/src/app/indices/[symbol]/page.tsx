import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";
import IndexDetailClient from "../../../components/IndexDetailClient";
import { authOptions } from "../../api/auth/[...nextauth]/route";

interface QuoteItem {
  date: string;
  close: number;
  open: number;
  high: number;
  low: number;
  volume: number;
}

async function fetchQuotes(symbol: string, token: string) {
  const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/indices/${symbol}/quotes?page=1&page_size=20`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
    cache: "no-store",
  });
  if (!res.ok) return { items: [], total: 0 };
  return await res.json();
}

async function fetchLatest(symbol: string, token: string) {
  const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/indices/${symbol}/latest`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
    cache: "no-store",
  });
  if (!res.ok) return null;
  return await res.json();
}

export default async function IndexDetailPage({ params }: { params: { symbol: string } }) {
  let session = await getServerSession(authOptions);
  let token = "";

  // 开发环境下跳过登录校验，构造 mock session 和 token
  if (process.env.NODE_ENV === 'development') {
    session = { user: { id: 'dev', name: 'dev' }, expires: '2099-12-31T23:59:59.999Z' };
    token = "dev-token";
  } else {
    if (!session) {
      redirect("/auth/signin");
    }
    token = (session as any).token || "";
  }

  const symbol = params.symbol;
  const quotesData = await fetchQuotes(symbol, token);
  const latest = await fetchLatest(symbol, token);

  return (
    <IndexDetailClient
      symbol={symbol}
      quotes={quotesData.items || []}
      latest={latest}
      total={quotesData.total || 0}
      pageSize={20}
      session={session}
    />
  );
}