import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";
import IndicesClient from "../../components/IndicesClient";
import { authOptions } from "../api/auth/[...nextauth]/route";

interface IndexItem {
  symbol: string;
  name: string;
  latest_close: number;
  latest_change: number;
  latest_change_percent: number;
}

async function fetchIndices(token: string): Promise<IndexItem[]> {
  // 假设后端API需要token，实际可根据API实现调整
  const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/indices`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
    cache: "no-store",
  });
  if (!res.ok) return [];
  return await res.json();
}

export default async function IndicesPage() {
  let session = await getServerSession(authOptions);
  let token = "";

  // 开发环境下跳过登录校验，构造 mock session 和 token
  if (process.env.NODE_ENV === 'development') {
    session = { user: { id: 'dev', name: 'dev' }, expires: '2099-12-31T23:59:59.999Z' };
    token = "dev-token";
  } else {
    if (!session) {
      redirect("/auth/signin");
    }
    token = (session as any).token || "";
  }

  const indices = await fetchIndices(token);

  return <IndicesClient indices={indices} session={session} />;
}