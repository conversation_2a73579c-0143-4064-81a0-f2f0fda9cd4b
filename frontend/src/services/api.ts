import axios from 'axios';

// API基础URL
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:6008/api';

// 创建axios实例
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 自动注入 NextAuth token
import { getSession } from "next-auth/react";
apiClient.interceptors.request.use(
  async (config) => {
    const session = await getSession();
    const token =
      (session as any)?.accessToken ||
      (session as any)?.idToken ||
      (session as any)?.token;
    if (token) {
      config.headers = config.headers || {};
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// 指数行情相关API
export const getIndices = async () => {
  try {
    const response = await apiClient.get('/indices');
    return response.data;
  } catch (error) {
    throw new Error('无法获取指数行情数据');
  }
};

export const getIndexDetail = async (symbol: string) => {
  try {
    const response = await apiClient.get(`/indices/${symbol}`);
    return response.data;
  } catch (error) {
    throw new Error('无法获取指数详情');
  }
};