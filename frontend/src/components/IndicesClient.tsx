"use client";

import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useEffect, useState } from "react";

interface IndexItem {
  symbol: string;
  name: string;
  latest_close: number;
  latest_change: number;
  latest_change_percent: number;
}

export default function IndicesClient({
  indices,
  session,
}: {
  indices: IndexItem[];
  session: any;
}) {
  const { status } = useSession();
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (process.env.NODE_ENV === "development") return;
    if (status === "loading") return;
    if (!session) {
      router.replace("/auth/signin");
    }
  }, [session, status, router]);

  if (status === "loading" || loading) {
    return <div className="flex justify-center items-center h-64">加载中...</div>;
  }

  return (
    <div className="p-4 max-w-2xl mx-auto">
      <h1 className="text-2xl font-bold mb-4 text-center">A股主要宽基指数</h1>
      <table className="w-full border rounded-lg overflow-hidden text-sm">
        <thead className="bg-gray-100">
          <tr>
            <th className="py-2 px-2">名称</th>
            <th className="py-2 px-2">代码</th>
            <th className="py-2 px-2">最新收盘</th>
            <th className="py-2 px-2">涨跌</th>
            <th className="py-2 px-2">涨跌幅</th>
            <th className="py-2 px-2">详情</th>
          </tr>
        </thead>
        <tbody>
          {indices.map((idx) => (
            <tr key={idx.symbol} className="border-t">
              <td className="py-2 px-2">{idx.name}</td>
              <td className="py-2 px-2">{idx.symbol}</td>
              <td className="py-2 px-2">{idx.latest_close}</td>
              <td className={`py-2 px-2 ${idx.latest_change >= 0 ? "text-red-600" : "text-green-600"}`}>
                {idx.latest_change}
              </td>
              <td className={`py-2 px-2 ${idx.latest_change_percent >= 0 ? "text-red-600" : "text-green-600"}`}>
                {idx.latest_change_percent}%
              </td>
              <td className="py-2 px-2">
                <Link
                  href={`/indices/${idx.symbol}`}
                  className="text-blue-600 underline"
                >
                  查看
                </Link>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}