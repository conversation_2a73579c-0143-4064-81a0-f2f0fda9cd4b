"use client";

import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useEffect, useState } from "react";

interface QuoteItem {
  date: string;
  close: number;
  open: number;
  high: number;
  low: number;
  volume: number;
}

export default function IndexDetailClient({
  symbol,
  quotes,
  latest,
  total,
  pageSize,
  session,
}: {
  symbol: string;
  quotes: QuoteItem[];
  latest: any;
  total: number;
  pageSize: number;
  session: any;
}) {
  const { status } = useSession();
  const router = useRouter();
  const [page, setPage] = useState(1);
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (process.env.NODE_ENV === "development") return;
    if (status === "loading") return;
    if (!session) {
      router.replace("/auth/signin");
    }
  }, [session, status, router]);

  const totalPages = Math.ceil(total / pageSize);

  function LineChart({ data }: { data: QuoteItem[] }) {
    if (data.length === 0) return <div>暂无数据</div>;
    const w = 320, h = 120, pad = 30;
    const closes = data.map(q => q.close);
    const min = Math.min(...closes), max = Math.max(...closes);
    const points = closes.map((c, i) => {
      const x = pad + ((w - 2 * pad) * i) / (closes.length - 1);
      const y = h - pad - ((h - 2 * pad) * (c - min)) / (max - min || 1);
      return `${x},${y}`;
    }).join(" ");
    return (
      <svg width={w} height={h} className="my-4 bg-white rounded border">
        <polyline
          fill="none"
          stroke="#2563eb"
          strokeWidth="2"
          points={points}
        />
        <text x={2} y={h - pad} fontSize="10">{min}</text>
        <text x={2} y={pad} fontSize="10">{max}</text>
      </svg>
    );
  }

  if (status === "loading" || loading) {
    return <div className="flex justify-center items-center h-64">加载中...</div>;
  }

  return (
    <div className="p-4 max-w-2xl mx-auto">
      <Link href="/indices" className="text-blue-600 underline mb-2 block">← 返回列表</Link>
      <h2 className="text-xl font-bold mb-2">{symbol} 指数详情</h2>
      {latest && (
        <div className="mb-4">
          <span>最新收盘价：</span>
          <span className="font-bold">{latest.close}</span>
          <span className={latest.change >= 0 ? "text-red-600 ml-2" : "text-green-600 ml-2"}>
            {latest.change} ({latest.change_percent}%)
          </span>
        </div>
      )}
      <div>
        <label className="mr-2">起始日期：</label>
        <input
          type="date"
          value={startDate}
          onChange={e => setStartDate(e.target.value)}
          className="border rounded px-2 py-1 mr-4"
        />
        <label className="mr-2">结束日期：</label>
        <input
          type="date"
          value={endDate}
          onChange={e => setEndDate(e.target.value)}
          className="border rounded px-2 py-1"
        />
        <button
          className="ml-4 px-3 py-1 bg-blue-600 text-white rounded"
          onClick={() => setPage(1)}
        >
          筛选
        </button>
      </div>
      <LineChart data={quotes} />
      <table className="w-full border rounded-lg overflow-hidden text-xs mt-2">
        <thead className="bg-gray-100">
          <tr>
            <th className="py-1 px-1">日期</th>
            <th className="py-1 px-1">收盘</th>
            <th className="py-1 px-1">开盘</th>
            <th className="py-1 px-1">最高</th>
            <th className="py-1 px-1">最低</th>
            <th className="py-1 px-1">成交量</th>
          </tr>
        </thead>
        <tbody>
          {quotes.map(q => (
            <tr key={q.date} className="border-t">
              <td className="py-1 px-1">{q.date}</td>
              <td className="py-1 px-1">{q.close}</td>
              <td className="py-1 px-1">{q.open}</td>
              <td className="py-1 px-1">{q.high}</td>
              <td className="py-1 px-1">{q.low}</td>
              <td className="py-1 px-1">{q.volume}</td>
            </tr>
          ))}
        </tbody>
      </table>
      {/* 分页控件 */}
      <div className="flex justify-center items-center mt-4 gap-2">
        <button
          disabled={page <= 1}
          className="px-2 py-1 border rounded disabled:opacity-50"
          onClick={() => setPage(page - 1)}
        >
          上一页
        </button>
        <span>第 {page} / {totalPages} 页</span>
        <button
          disabled={page >= totalPages}
          className="px-2 py-1 border rounded disabled:opacity-50"
          onClick={() => setPage(page + 1)}
        >
          下一页
        </button>
      </div>
    </div>
  );
}