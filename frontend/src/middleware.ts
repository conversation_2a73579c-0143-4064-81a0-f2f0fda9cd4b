import { withAuth } from "next-auth/middleware";
import { NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";

const isDevelopment = process.env.NODE_ENV === 'development';

export default isDevelopment
  ? function middleware() {
      return NextResponse.next();
    }
  : withAuth(
      async function middleware(req) {
        const token = await getToken({ req });
        const isAuth = !!token;
        const isAuthPage = req.nextUrl.pathname.startsWith("/auth");

        if (isAuthPage) {
          if (isAuth) {
            return NextResponse.redirect(new URL("/", req.url));
          }
          return null;
        }

        if (!isAuth) {
          let from = req.nextUrl.pathname;
          if (req.nextUrl.search) {
            from += req.nextUrl.search;
          }

          return NextResponse.redirect(
            new URL(`/auth/signin?from=${encodeURIComponent(from)}`, req.url)
          );
        }
      },
      {
        callbacks: {
          authorized: ({ token }) => !!token,
        },
      }
    );

export const config = {
  matcher: [
    "/((?!api|_next/static|_next/image|favicon.ico|auth/signin).*)"]
};