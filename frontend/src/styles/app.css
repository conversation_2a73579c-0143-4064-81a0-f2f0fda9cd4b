/* 通用布局样式 */
.root-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  width: 100vw;
  overflow: hidden;
}
.topbar {
  box-shadow: 0 2px 8px 0 rgba(0,0,0,0.04);
  border-bottom: 1px solid #e3e3e3;
  min-height: 56px;
  background-color: #f8f9fa !important;
}
.navbar-brand {
  display: flex;
  align-items: center;
  font-size: 1.5rem;
  font-weight: 700;
  letter-spacing: 1px;
  color: #212529 !important;
  background: none;
  margin-left: 0.2rem;
  margin-right: 1.2rem;
  user-select: none;
}
.navbar-icon {
  font-size: 1.3em;
  margin-right: 0.38em;
  color: #6c757d;
  display: inline-flex;
  align-items: center;
  vertical-align: middle;
  user-select: none;
}
.mainrow {
  flex: 1 1 0;
  display: flex;
  min-height: 0;
  width: 100%;
}
.sidebar {
  min-width: 200px;
  max-width: 240px;
  height: 100%;
  box-shadow: 2px 0 12px 0 rgba(0,123,255,0.04);
  border-right: 1.5px solid #e3e3e3;
  background: #f8fafc;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  padding-left: 0;
  padding-right: 0;
}
.sidebar-item {
  font-size: 1.08rem;
  font-weight: 500;
  color: #3a4a5a !important;
  border-radius: 0.7rem 1.2rem 1.2rem 0;
  margin-right: 8px;
  padding: 0.7rem 1.2rem 0.7rem 1.5rem;
  background: none;
  border: none;
  text-align: left;
  transition: background 0.18s, color 0.18s;
}
.sidebar-item:hover,
.sidebar-item:focus {
  background: linear-gradient(90deg, #e0f2ff 0%, #f0faff 100%);
  color: #007bff !important;
}
.sidebar-item.active {
  background: linear-gradient(90deg, #d0e7ff 0%, #e0f7ff 100%);
  color: #007bff !important;
  font-weight: 700;
  box-shadow: 0 2px 8px 0 rgba(0,123,255,0.06);
}
.maincontent {
  background: #fff;
  flex: 1 1 0;
  min-height: 0;
  height: 100%;
  box-shadow: none;
  padding: 0.5rem 0.5rem 0.5rem 0.5rem;
  border-left: none;
  border-radius: 0 1.5rem 1.5rem 0;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  overflow: auto;
}

/* 响应式适配 */
@media (max-width: 900px) {
  .sidebar {
    min-width: 120px;
    max-width: 160px;
    padding-left: 0.2rem;
    padding-right: 0.2rem;
  }
  .maincontent {
    padding: 1.2rem 0.5rem 1rem 0.5rem;
    border-radius: 0;
  }
}
@media (max-width: 600px) {
  .topbar {
    min-height: 48px;
    padding-left: 0.7rem;
    padding-right: 0.7rem;
    border-radius: 0;
  }
  .navbar-brand {
    font-size: 1.1rem;
    margin-right: 0.5rem;
  }
  .modal-dialog {
    max-width: 95vw !important;
    width: auto !important;
    margin: 0 auto !important;
  }
  .modal-content {
    max-height: 90vh !important;
    overflow-y: auto !important;
    border-radius: 16px !important;
  }
  .modal-body {
    padding: 1rem !important;
  }
  .modal-header, .modal-footer {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }
}