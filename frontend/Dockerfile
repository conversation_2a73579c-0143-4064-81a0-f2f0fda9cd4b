# -------- dependencies 阶段 --------
FROM node:18-alpine AS dependencies

WORKDIR /app

# 只复制依赖相关文件，加快缓存利用
COPY package.json yarn.lock ./

# 安装依赖（生产和开发依赖都装，供后续构建用）
RUN yarn install --frozen-lockfile

# -------- builder 阶段 --------
FROM node:18-alpine AS builder

WORKDIR /app

# 复制依赖
COPY --from=dependencies /app/node_modules ./node_modules
COPY package.json yarn.lock ./
# 复制全部源代码
COPY . .

# 设置环境变量
ARG NEXT_PUBLIC_API_URL
ENV NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}
ARG NEXT_PUBLIC_TITLE
ENV NEXT_PUBLIC_TITLE=${NEXT_PUBLIC_TITLE}
ARG NEXT_PUBLIC_SSO_ENABLED
ENV NEXT_PUBLIC_SSO_ENABLED=${NEXT_PUBLIC_SSO_ENABLED}

# 构建 Next.js 独立运行产物
RUN yarn build

# -------- runner 阶段 --------
FROM node:18-alpine AS runner

WORKDIR /app

# 设置环境变量
ARG NEXT_PUBLIC_API_URL
ENV NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}
ARG NEXT_PUBLIC_TITLE
ENV NEXT_PUBLIC_TITLE=${NEXT_PUBLIC_TITLE}
ARG NEXT_PUBLIC_SSO_ENABLED
ENV NEXT_PUBLIC_SSO_ENABLED=${NEXT_PUBLIC_SSO_ENABLED}

ENV NODE_ENV=production
ENV PORT=6006

# 只复制 Next.js 独立运行所需文件
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/static ./.next/static

EXPOSE 6006

# 启动 Next.js 独立服务器
CMD ["node", "server.js"]